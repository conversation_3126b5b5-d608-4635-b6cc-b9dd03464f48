import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Client as FTPClient } from 'basic-ftp';
import { FTP_HOST, FTP_USER, FTP_PASSWORD, FTP_PATH } from 'src/lib/config';
import { Readable } from 'stream';

// Use service role key for full database access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables for backup service');
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Define table names to backup
const TABLES_TO_BACKUP = ['contact', 'bookmark','profiles'];

/**
 * Convert array of objects to CSV format
 */
function arrayToCSV(data: any[]): string {
  if (!data || data.length === 0) {
    return '';
  }

  // Get headers from the first object
  const headers = Object.keys(data[0]);
  
  // Create CSV header row
  const csvHeaders = headers.map(header => `"${header}"`).join(',');
  
  // Create CSV data rows
  const csvRows = data.map(row => {
    return headers.map(header => {
      let value = row[header];
      
      // Handle null/undefined values
      if (value === null || value === undefined) {
        value = '';
      }
      
      // Handle objects/arrays (JSON columns)
      if (typeof value === 'object') {
        value = JSON.stringify(value);
      }
      
      // Escape quotes and wrap in quotes
      value = String(value).replace(/"/g, '""');
      return `"${value}"`;
    }).join(',');
  });
  
  return [csvHeaders, ...csvRows].join('\n');
}

/**
 * Generate filename with current date and time
 */
function generateFilename(tableName: string): string {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 19).replace(/[T:]/g, '-');
  return `${tableName}_${dateStr}.csv`;
}

/**
 * Export table data as CSV
 */
async function exportTableToCSV(tableName: string): Promise<{ filename: string; csvData: string }> {
  try {
    const { data, error } = await supabaseAdmin
      .from(tableName)
      .select('*');

    if (error) {
      throw new Error(`Failed to fetch data from ${tableName}: ${error.message}`);
    }

    const csvData = arrayToCSV(data || []);
    const filename = generateFilename(tableName);

    return { filename, csvData };
  } catch (error) {
    throw new Error(`Error exporting ${tableName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload CSV data to FTP server
 */
async function uploadToFTP(filename: string, csvData: string): Promise<void> {
  const client = new FTPClient();
  
  try {
    // Connect to FTP server
    await client.access({
      host: FTP_HOST,
      user: FTP_USER,
      password: FTP_PASSWORD,
      secure: false // Set to true if using FTPS
    });

    // Ensure backup directory exists
    try {
      await client.ensureDir(FTP_PATH);
    } catch (error) {
      // Directory might already exist, continue
      console.log('FTP directory might already exist:', error);
    }

    // Upload the CSV data
   const stream = Readable.from([csvData]);
    await client.uploadFrom(stream, `${FTP_PATH}${filename}`);
    
    console.log(`Successfully uploaded ${filename} to FTP`);
  } catch (error) {
    throw new Error(`FTP upload failed for ${filename}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    client.close();
  }
}

/**
 * Main backup function
 */
async function performBackup(): Promise<{ success: boolean; message: string; files: string[] }> {
  const uploadedFiles: string[] = [];
  const errors: string[] = [];

  for (const tableName of TABLES_TO_BACKUP) {
    try {
      console.log(`Starting backup for table: ${tableName}`);
      
      // Export table to CSV
      const { filename, csvData } = await exportTableToCSV(tableName);
      
      // Upload to FTP
      await uploadToFTP(filename, csvData);
      
      uploadedFiles.push(filename);
      console.log(`Successfully backed up ${tableName} as ${filename}`);
    } catch (error) {
      const errorMessage = `Failed to backup ${tableName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      errors.push(errorMessage);
      console.error(errorMessage);
    }
  }

  const success = errors.length === 0;
  const message = success 
    ? `Successfully backed up ${uploadedFiles.length} tables`
    : `Backup completed with ${errors.length} errors: ${errors.join('; ')}`;

  return { success, message, files: uploadedFiles };
}

/**
 * API Route Handler
 */
export async function POST(request: NextRequest) {
  try {
    // Optional: Add authentication/authorization here
    // For now, we'll rely on the fact that this is a server-side only endpoint
    
    console.log('Starting Supabase backup process...');
    
    const result = await performBackup();
    
    return NextResponse.json({
      success: result.success,
      message: result.message,
      files: result.files,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Backup process failed:', error);
    
    return NextResponse.json({
      success: false,
      message: `Backup process failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      files: [],
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Also support GET requests for easier curl usage
export async function GET(request: NextRequest) {
  return POST(request);
}
